//
//  ChannelHScrollView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/31/25.
//

import SwiftUI

struct ChannelHScrollView: View {
    let channels: [UIChannel]
    let onSelectedChannel: (UIChannel) -> Void
    var onUnfollowed: ((UIChannel) -> Void)?
    
    @Environment(\.channelService) private var channelService
    
    @State private var followStatus: [String: Bool] = [:]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Constants.cardSpacing) {
                ForEach(channels) { channel in
                    Button {
                        onSelectedChannel(channel)
                    } label: {
                        ChannelCard(channel: channel, hideVerifiedIcon: true)
                            .frame(width: Constants.channelCardSize)
                    }
                    .buttonBorderShape(.circle)
                    .contextMenu {
                        contextMenu(channel: channel)
                    }
                }
            }
            .buttonStyle(.borderless)
            .padding(.horizontal, Constants.cardPadding)
            .padding(.vertical, Constants.cardPadding*4)
        }
        .onAppear {
            // Initialize status dictionaries when view appears
            for channel in channels {
                followStatus[channel.id] = channelService.isFollowing(channelId: channel.id)
            }
        }
    }
    
    @ViewBuilder
    func contextMenu(channel: UIChannel) -> some View {
        CardContextMenu(
            channel: channel,
            followStatus: Binding(
                get: { followStatus[channel.id] ?? false },
                set: { followStatus[channel.id] = $0 }
            ),
            onFollowStatusChanged: { newStatus in
                if !newStatus {
                    onUnfollowed?(channel)
                }
            }
        )
        .onAppear {
            if followStatus[channel.id] == nil {
                followStatus[channel.id] = channelService.isFollowing(channelId: channel.id)
            }
        }
    }
}

#Preview {
    VStack {
        ChannelHScrollView(
            channels: StreamPreview.createSampleUIChannels(count: 10),
            onSelectedChannel: { _ in }
        )
        
        ChannelHScrollView(
            channels: [.init(
                id: "id",
                name: "Lorem ipsum dolor sit amet, consectetur adipiscing elit",
                imageUrl: URL(string: "https://picsum.photos/100")!)],
            onSelectedChannel: { _ in }
        )
    }
}
