import SwiftUI

struct ChannelHeaderView: View {
    let channel: UIChannel
    let isFollowing: Bool
    let isHidden: Bool
    let onFollowToggle: () -> Void
    
    var body: some View {
        HStack(alignment: .top, spacing: Constants.channelInfoSpacing) {
            if let imageUrl = channel.imageUrl {
                AsyncImage(url: imageUrl) { image in
                    image
                        .resizable()
                        .scaledToFill()
                } placeholder: {
                    Rectangle()
                        .foregroundColor(.gray.opacity(0.3))
                }
                .frame(width: Constants.channelInfoImageSize,
                       height: Constants.channelInfoImageSize)
                .clipShape(Circle())
            } else {
                Image(systemName: "person.circle.fill")
                    .resizable()
                    .foregroundColor(.gray)
                    .frame(width: 80, height: 80)
            }
            
            VStack(alignment: .leading, spacing: Constants.channelInfoSpacing) {
                VStack(alignment: .leading, spacing: 12) {
                    Text(channel.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(channel.followerCountFormatted)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    if let description = channel.description {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
#if os(tvOS)
                            .lineLimit(2)
#endif
                    }
                }
                
                HStack(spacing: 40) {
                    // Follow button
                    Button(action: onFollowToggle) {
                        if #available(iOS 26.0, *) {
                            Label(
                                isFollowing ? String(localized: "Unfollow") : String(localized: "Follow"),
                                systemImage: isFollowing ? "heart.fill" : "heart"
                            )
#if os(tvOS)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
#else
                            .padding()
                            .background(.chzzk)
                            .foregroundStyle(.background)
                            .clipShape(Capsule())
#endif
                            
                        } else {
                            // Fallback on earlier versions
                        }
                    }
                }
            }
        }
    }
}

#Preview {
    ChannelHeaderView(
        channel: StreamPreview.sampleUIChannel,
        isFollowing: false,
        isHidden: false,
        onFollowToggle: {}
    )
    .padding()
} 
