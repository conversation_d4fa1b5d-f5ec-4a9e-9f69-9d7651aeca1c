import SwiftUI
import SwiftData

/// A view that displays detailed information about a channel, including its live stream and videos.
struct ChannelView: View {
    // MARK: - Properties
    
    let channelId: String
    @StateObject private var viewModel: ChannelViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.channelService) private var channelService
    @Environment(\.videoService) private var videoService
    
    @State private var isLoading = false
    @State private var isFollowing = false
    @State private var isHidden = false
    @State private var selectedVideo: UIVideo? = nil
    @State private var selectedStream: UILiveStream? = nil
    
    // MARK: - Initialization
    
    init(channelId: String, viewModel: ChannelViewModel) {
        self.channelId = channelId
        _viewModel = .init(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 16) {
                if let channel = viewModel.channel {
                    VStack(spacing: 40) {
                        channelHeaderSection(channel)
                        
                        Divider()
                        
                        videosSection
                    }
                }
                
                // Bottom detector for pagination
                if viewModel.hasMoreVideos && !viewModel.videos.isEmpty && !viewModel.isLoadingVideos {
                    Color.clear
                        .frame(height: 50)
                        .onAppear {
                            Task {
                                await viewModel.loadChannelVideos(channelId: channelId, usePagination: true)
                            }
                        }
                }
                
                // Loading indicator
                if viewModel.isLoadingVideos && viewModel.videos.count > 0 {
                    HStack {
                        Spacer()
                        LoadingView()
                        Spacer()
                    }
                    .padding()
                }
            }
            .padding(.top)
        }
        .padding(.horizontal)
        .task {
            await loadInitialData()
        }
        .fullScreenCover(item: $selectedVideo) { video in
            VideoDetailView(video: video, viewModel: .init(
                videoService: videoService
            ))
        }
        .fullScreenCover(item: $selectedStream) { stream in
            LiveDetailView(stream: stream, viewModel: .init(
                channelService: channelService
            ))
        }
        .presentationBackground(.thickMaterial)
    }
    
    // MARK: - View Components
    
    private func channelHeaderSection(_ channel: UIChannel) -> some View {
        HStack(alignment: .top, spacing: 16) {
            VStack(spacing: Constants.channelInfoSpacing) {
                ChannelHeaderView(
                    channel: channel,
                    isFollowing: isFollowing,
                    isHidden: isHidden,
                    onFollowToggle: { toggleFollow() }
                )
#if os(iOS)
                if let liveStream = viewModel.liveStream {
                    LiveStreamCardView(stream: liveStream) {
                        selectedStream = liveStream
                    }
                    .clipShape(.rect(cornerRadius: Constants.cornerRadius))
                }
#endif
            }
            .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
#if os(tvOS)
            if let liveStream = viewModel.liveStream {
                LiveStreamCardView(stream: liveStream) {
                    selectedStream = liveStream
                }
                .frame(width: 450, alignment: .leading)
                .padding(.trailing, 100)
            }
#endif
        }
#if os(tvOS)
        .frame(height: 300)
        .padding()
#else
        .padding(.horizontal)
#endif
    }
    
    @ViewBuilder
    private var videosSection: some View {
        VStack(spacing: 0) {
            if !viewModel.videos.isEmpty {
                VideoVScrollView(
                    videos: viewModel.videos,
                    selectedVideo: $selectedVideo,
                )
            } else if viewModel.isLoadingVideos {
                LoadingView()
            } else if let error = viewModel.error {
                ErrorView(
                    title: "Failed to load videos",
                    message: error.localizedDescription,
                    isRetrying: viewModel.isLoadingVideos) {
                        retryLoadingVideos()
                    }
            } else {
                VStack(spacing: Constants.channelInfoSpacing) {
                    Image(systemName: "tv.slash")
                        .resizable()
                        .frame(width: 50, height: 50)
                    Text("No Video")
                }
                .foregroundStyle(.secondary)
            }
        }
    }
    
    // MARK: - Actions
    
    private func loadInitialData() async {
        isFollowing = viewModel.isFollowing(channelId: channelId)
        await viewModel.loadAll(channelId: channelId)
    }
    
    private func toggleFollow() {
        Task {
            if let isNowFollowing = await viewModel.toggleFollow() {
                isFollowing = isNowFollowing
            }
        }
    }
    
    private func retryLoadingVideos() {
        viewModel.error = nil
        Task {
            await viewModel.loadChannelVideos(
                channelId: channelId,
                usePagination: false
            )
        }
    }
}

// MARK: - Preview

#Preview {
    ChannelView(channelId: "1",
                viewModel: ChannelViewModel(
                    channelService: PreviewChannelService()
                )
    )
}
